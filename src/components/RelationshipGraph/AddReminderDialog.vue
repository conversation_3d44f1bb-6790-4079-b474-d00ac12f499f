<template>
  <div v-if="show" class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <div class="dialog-title">添加提醒</div>
        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>

      <div class="dialog-content">
        <!-- 引导词区域 -->
        <div class="guide-section">
          <div class="guide-text">
            <div class="guide-title">🎯 智能提醒助手</div>
            <div class="guide-description">用自然语言描述您的提醒需求，我会帮您智能创建提醒事项</div>
            <div class="guide-examples">
              <div class="example-item">💡 "明天上午9点开会，提前30分钟提醒我"</div>
              <div class="example-item">💡 "每周五下午5点提醒我写周报"</div>
              <div class="example-item">💡 "下个月15号是妈妈生日，提前一天提醒"</div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-section">
          <!-- 识别文字显示区域 -->
          <div v-if="recognizedText" class="recognized-text">
            {{ recognizedText }}
          </div>

          <!-- 默认状态：左侧语音按钮 + 中间输入框 + 右侧发送按钮 -->
          <div v-if="!isRecording" class="input-default">
            <button class="voice-mic-btn" @click="startVoiceRecording">
              <div class="voice-button-bg"></div>
              <img src="@/assets/icon/mic.png" alt="语音" class="voice-mic-icon" />
            </button>

            <div class="text-input-container">
              <input
                v-model="textInput"
                type="text"
                placeholder="描述您的提醒需求..."
                class="text-input"
                @keydown.enter="handleTextSend"
              />
            </div>

            <button
              class="send-btn"
              :class="{
                'not-input': !textInput.trim() || isSubmitting,
              }"
              @click="handleTextSend"
            >
              <i class="iconfont icon-mobile-send" class-prefix="icon"></i>
            </button>
          </div>

          <!-- 录音状态：左侧麦克风 + 语音条 -->
          <div v-else class="input-recording">
            <div class="recording-mic-container">
              <button class="recording-mic-btn" @click="startVoiceRecording">
                <div class="voice-button-bg recording"></div>
                <img src="@/assets/icon/mic.png" alt="录音中" class="recording-mic-icon" />
              </button>
            </div>
            <div class="voice-wave">
              <div v-for="index in 50" :key="index" class="wave-line"></div>
            </div>
          </div>
        </div>

        <!-- 提交状态显示 -->
        <div v-if="isSubmitting" class="submitting-section">
          <div class="loading">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
          <div class="submitting-text">正在创建提醒...</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { addReminderNatural, type IAddReminderNaturalRequest } from '@/apis/memory';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';

// Props定义
interface IProps {
  show: boolean;
  userId: string;
  personId?: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  success: [];
}>();

// 响应式数据
const isSubmitting = ref(false);

// 语音录音相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref<ArrayBuffer | null>(null);

// 文字输入相关状态
const textInput = ref('');
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 重置状态
const resetState = () => {
  textInput.value = '';
  recognizedText.value = '';
  isSubmitting.value = false;
  if (isRecording.value) {
    cancelRecording();
  }
  releaseMicrophoneResources();
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
};

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (streamData.data.text) {
        recognizedText.value = streamData.data.full_text;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  releaseMicrophoneResources();
  recognizedText.value = '';
};

// 开始语音录音
const startVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，取消录音
    cancelRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    // 直接发送语音识别的文字
    await handleVoiceSend(recognizedText.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
};

// 处理文字发送
const handleTextSend = async () => {
  // 如果输入框为空或正在提交中，直接返回
  if (!textInput.value.trim() || isSubmitting.value) return;

  const content = textInput.value.trim();
  await handleSubmitReminder(content);
};

// 处理语音发送
const handleVoiceSend = async (content: string) => {
  await handleSubmitReminder(content);
};

// 提交提醒
const handleSubmitReminder = async (naturalText: string) => {
  if (!naturalText.trim() || isSubmitting.value) return;

  console.log('🚀 [AddReminderDialog] 开始提交自然语言提醒:', naturalText);

  try {
    isSubmitting.value = true;

    // 构建请求参数
    const requestData: IAddReminderNaturalRequest = {
      user_id: props.userId,
      natural_text: naturalText,
    };

    console.log('📤 [AddReminderDialog] 自然语言提醒请求参数:', requestData);

    // 调用自然语言添加提醒API
    const response = await addReminderNatural(requestData);

    console.log('� [AddReminderDialog] 自然语言提醒响应:', response);

    if (response && response.success) {
      console.log('✅ [AddReminderDialog] 提醒添加成功');
      showSuccessToast('提醒添加成功！');
      emit('success');
      handleClose();
    } else {
      console.warn('⚠️ [AddReminderDialog] 添加提醒失败:', response);
      showFailToast((response?.message as string) || '添加提醒失败');
    }
  } catch (error) {
    console.error('❌ [AddReminderDialog] 提交提醒失败:', error);
    showFailToast('网络错误，添加提醒失败');
  } finally {
    isSubmitting.value = false;
  }
};

// 监听show变化，重置状态
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      resetState();
    }
  },
);

// 处理关闭
const handleClose = () => {
  if (isSubmitting.value) return;

  resetState();
  emit('close');
};

// 组件卸载时清理
onBeforeUnmount(() => {
  resetState();
});
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00bcd4;
  box-shadow: -4px 0 8px rgba(0, 188, 212, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 188, 212, 0.2);

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    font-weight: 600;
    flex-shrink: 0;
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }

  // 引导区域
  .guide-section {
    background: rgba(0, 188, 212, 0.05);
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    padding: 24px;
    backdrop-filter: blur(10px);

    .guide-text {
      .guide-title {
        color: rgba(255, 255, 255, 0.9);
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 12px;
        text-align: center;
      }

      .guide-description {
        color: rgba(255, 255, 255, 0.8);
        font-size: 20px;
        text-align: center;
        margin-bottom: 20px;
        line-height: 1.5;
      }

      .guide-examples {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .example-item {
          color: rgba(0, 188, 212, 0.9);
          font-size: 18px;
          padding: 8px 12px;
          background: rgba(0, 188, 212, 0.1);
          border-radius: 8px;
          border-left: 3px solid #00bcd4;
        }
      }
    }
  }

  // 输入区域
  .input-section {
    background: rgba(0, 188, 212, 0.05);
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: blur(10px);

    // 识别文字显示区域
    .recognized-text {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 12px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 20px;
      text-align: center;
      margin-bottom: 16px;
      word-wrap: break-word;
    }

    // 默认状态：左侧语音按钮 + 中间输入框 + 右侧发送按钮
    .input-default {
      display: flex;
      align-items: center;
      width: 100%;
      gap: 16px;

      .voice-mic-btn {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        border: 2px solid #00bcd4;
        background: rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        backdrop-filter: blur(20px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        flex-shrink: 0;

        &:hover {
          background: rgba(0, 188, 212, 0.1);
          border-color: #00bcd4;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        }

        .voice-button-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          transition: all 0.3s ease;
        }

        .voice-mic-icon {
          width: 24px;
          height: 24px;
          filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
            contrast(96%);
          position: relative;
          z-index: 2;
        }

        &:active {
          transform: scale(0.95);
        }
      }

      .text-input-container {
        flex: 1;
        display: flex;
        align-items: center;

        .text-input {
          width: 100%;
          height: 56px;
          background: rgba(255, 255, 255, 0.1);
          border: 2px solid rgba(255, 255, 255, 0.2);
          border-radius: 28px;
          padding: 0 20px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 20px;
          max-height: 165px;
          font-weight: 600;
          outline: none;
          transition: all 0.3s ease;
          backdrop-filter: blur(20px);

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          &:focus {
            border-color: #00bcd4;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 4px rgba(0, 188, 212, 0.1);
          }
        }
      }

      .send-btn {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        border: 2px solid #00bcd4;
        background: rgba(0, 188, 212, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(20px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        flex-shrink: 0;

        &:hover:not(.not-input) {
          background: rgba(0, 188, 212, 0.2);
          border-color: #00bcd4;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        }

        &.not-input {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        .iconfont {
          font-size: 40px;
          color: #00bcd4;
        }

        &:active:not(.not-input) {
          transform: scale(0.95);
        }
      }
    }

    // 录音状态：左侧麦克风 + 语音条
    .input-recording {
      display: flex;
      align-items: center;
      width: 100%;
      gap: 20px;

      .recording-mic-container {
        display: flex;
        align-items: center;
        justify-content: center;

        .recording-mic-btn {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          border: 2px solid #00bcd4;
          background: rgba(255, 255, 255, 0.1);
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          backdrop-filter: blur(20px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

          &:hover {
            background: rgba(0, 188, 212, 0.1);
            border-color: #00bcd4;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
          }

          .voice-button-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            transition: all 0.3s ease;

            &.recording {
              background: rgba(0, 188, 212, 0.2);
              border: 2px solid #00bcd4;
              animation: voiceRecording 2s ease-in-out infinite;
              box-shadow: 0 0 0 0 rgba(0, 188, 212, 0.3);
            }
          }

          .recording-mic-icon {
            width: 24px;
            height: 24px;
            filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg)
              brightness(97%) contrast(96%);
            position: relative;
            z-index: 2;
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }

      .voice-wave {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        height: 60px;

        .wave-line {
          width: 3px;
          background: linear-gradient(to top, #00bcd4, #00bcd4);
          border-radius: 2px;
          animation: waveAnimation 1.5s ease-in-out infinite;

          &:nth-child(odd) {
            animation-delay: 0.1s;
          }

          &:nth-child(even) {
            animation-delay: 0.3s;
          }
        }
      }
    }
  }

  // 提交状态区域
  .submitting-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: rgba(0, 188, 212, 0.05);
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    backdrop-filter: blur(10px);

    .submitting-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 20px;
      text-align: center;
    }
  }
}

// Loading动画
.loading {
  display: flex;
  gap: 4px;
  align-items: center;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    animation: loadingDot 1.4s infinite ease-in-out both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes loadingDot {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes voiceRecording {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 188, 212, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(0, 188, 212, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 20px rgba(0, 188, 212, 0);
  }
}

@keyframes waveAnimation {
  0%,
  100% {
    height: 10px;
  }
  50% {
    height: 40px;
  }
}
</style>
